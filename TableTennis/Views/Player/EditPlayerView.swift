import SwiftUI

struct EditPlayerView: View {
    @EnvironmentObject var dataManager: DataManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var playerName: String
    @State private var announcerName: String
    @State private var profileImageData: Data?
    @State private var showingAlert = false
    @State private var alertMessage = ""

    let player: Player

    init(player: Player) {
        self.player = player
        self._playerName = State(initialValue: player.name)
        self._announcerName = State(initialValue: player.announcerName)
        self._profileImageData = State(initialValue: player.profileImageData)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Player Information")) {
                    TextField("Name", text: $playerName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    HStack {
                        Text("Current ELO Rating:")
                            .font(.subheadline)
                        Spacer()
                        Text("\(Int(player.eloRating))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                }

                Section(header: Text("Announcer Settings")) {
                    TextField("Announcer Name", text: $announcerName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Text("This name will be used by the Mix & Match announcer. Leave empty to use the regular name.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Section(header: Text("Profile Photo")) {
                    PhotoPicker(selectedImageData: $profileImageData, maxImageSize: 80)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
                
                Section(header: Text("Statistics"), footer: Text("These statistics are automatically updated based on match results.")) {
                    HStack {
                        Text("Matches played")
                        Spacer()
                        Text("\(player.matchesPlayed)")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Matches won")
                        Spacer()
                        Text("\(player.matchesWon)")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Win percentage")
                        Spacer()
                        Text("\(Int(player.matchWinPercentage))%")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Games played")
                        Spacer()
                        Text("\(player.gamesPlayed)")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Games won")
                        Spacer()
                        Text("\(player.gamesWon)")
                            .foregroundColor(.secondary)
                    }
                }

                Section(footer: Text("ELO rating is automatically adjusted based on match results and cannot be manually changed.")) {
                    EmptyView()
                }
            }
            .navigationTitle("Edit Player")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        savePlayer()
                    }
                    .disabled(playerName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func savePlayer() {
        let trimmedName = playerName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Validation
        guard !trimmedName.isEmpty else {
            alertMessage = "Please enter a valid name."
            showingAlert = true
            return
        }

        // Check if another player with this name already exists (except current player)
        if dataManager.players.contains(where: { $0.name.lowercased() == trimmedName.lowercased() && $0.id != player.id }) {
            alertMessage = "Another player with this name already exists."
            showingAlert = true
            return
        }

        // Update player (only name and announcer name, ELO rating remains unchanged)
        var updatedPlayer = player
        updatedPlayer.name = trimmedName
        updatedPlayer.announcerName = announcerName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? trimmedName : announcerName.trimmingCharacters(in: .whitespacesAndNewlines)
        updatedPlayer.profileImageData = profileImageData

        dataManager.updatePlayer(updatedPlayer)
        
        presentationMode.wrappedValue.dismiss()
    }
}

#Preview {
    EditPlayerView(player: Player(name: "Test Speler", competitionId: UUID()))
        .environmentObject(DataManager())
}
