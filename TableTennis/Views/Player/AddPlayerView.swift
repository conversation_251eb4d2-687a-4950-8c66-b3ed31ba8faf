import SwiftUI

struct AddPlayerView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var playerName = ""
    @State private var announcerName = ""
    @State private var profileImageData: Data?
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Player Information")) {
                    TextField("Name", text: $playerName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    HStack {
                        Text("Starting ELO Rating:")
                            .font(.subheadline)
                        Spacer()
                        Text("1200")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                }

                Section(header: Text("Announcer Settings")) {
                    TextField("Announcer Name", text: $announcerName, prompt: Text("Optional - defaults to player name"))
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Text("This name will be used by the Mix & Match announcer. Leave empty to use the regular name.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Section(header: Text("Profile Photo")) {
                    PhotoPicker(selectedImageData: $profileImageData, maxImageSize: 80)
                        .frame(maxWidth: .infinity, alignment: .center)
                }

                Section(footer: Text("ELO rating will be automatically adjusted based on match results.")) {
                    EmptyView()
                }
            }
            .navigationTitle("New Player")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        savePlayer()
                    }
                    .disabled(playerName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func savePlayer() {
        let trimmedName = playerName.trimmingCharacters(in: .whitespacesAndNewlines)

        // Validation
        guard !trimmedName.isEmpty else {
            alertMessage = "Please enter a valid name."
            showingAlert = true
            return
        }

        guard let competition = cloudKitManager.currentCompetition else {
            alertMessage = "No competition selected."
            showingAlert = true
            return
        }

        // Check if player already exists (by name for user experience, but we use IDs internally)
        if dataManager.players.contains(where: { $0.name.lowercased() == trimmedName.lowercased() }) {
            alertMessage = "A player with this name already exists."
            showingAlert = true
            return
        }

        // Process announcer name (use player name if empty)
        let trimmedAnnouncerName = announcerName.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalAnnouncerName = trimmedAnnouncerName.isEmpty ? trimmedName : trimmedAnnouncerName

        // Create new player with fixed ELO rating of 1200
        let newPlayer = Player(
            name: trimmedName,
            competitionId: competition.id,
            eloRating: 1200.0,
            profileImageData: profileImageData,
            announcerName: finalAnnouncerName
        )
        dataManager.addPlayer(newPlayer)

        presentationMode.wrappedValue.dismiss()
    }
}

#Preview {
    AddPlayerView()
        .environmentObject(DataManager())
}
