<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Tennis - Beta Testing</title>
    <link rel="apple-touch-icon" href="app-icon.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            max-width: 400px;
            width: 90%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .app-icon {
            width: 120px;
            height: 120px;
            border-radius: 26px;
            margin: 0 auto 30px;
            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.3);
            position: relative;
            overflow: hidden;
            background: #f0f0f0;
        }

        .app-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 26px;
        }

        .app-icon::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
            border-radius: 26px;
            pointer-events: none;
        }

        h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #1d1d1f;
        }

        .subtitle {
            font-size: 18px;
            color: #86868b;
            margin-bottom: 30px;
            font-weight: 400;
        }

        .features {
            text-align: left;
            margin: 30px 0;
            padding: 0 10px;
        }

        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 16px;
            color: #1d1d1f;
        }

        .feature-icon {
            width: 24px;
            height: 24px;
            background: #007AFF;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
        }

        .download-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 12px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .download-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .download-btn.secondary {
            background: #34C759;
        }

        .download-btn.secondary:hover {
            background: #28a745;
            box-shadow: 0 8px 25px rgba(52, 199, 89, 0.3);
        }

        .download-options {
            margin-top: 20px;
        }

        .download-note {
            font-size: 12px;
            color: #86868b;
            margin-top: 15px;
            line-height: 1.4;
        }

        .beta-badge {
            background: linear-gradient(135deg, #FF9500, #FF6B35);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .instructions {
            background: #f2f2f7;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            text-align: left;
        }

        .instructions h3 {
            color: #1d1d1f;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .instructions ol {
            color: #86868b;
            font-size: 14px;
            line-height: 1.5;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
                margin: 20px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            .subtitle {
                font-size: 16px;
            }
        }

        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            }
            
            .container {
                background: rgba(28, 28, 30, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            h1, .feature {
                color: #f2f2f7;
            }
            
            .subtitle {
                color: #98989d;
            }
            
            .instructions {
                background: #1c1c1e;
            }
            
            .instructions h3 {
                color: #f2f2f7;
            }
            
            .instructions ol {
                color: #98989d;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="beta-badge">Beta Testing</div>
        
        <div class="app-icon">
            <img src="app-icon.png" alt="Table Tennis App Icon" />
        </div>
        
        <h1>Table Tennis</h1>
        <p class="subtitle">Professional Tournament Management</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🏆</div>
                <span>ELO Rating System</span>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <span>Live Match Tracking</span>
            </div>
            <div class="feature">
                <div class="feature-icon">☁️</div>
                <span>CloudKit Sync</span>
            </div>
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <span>Flic Button Support</span>
            </div>
        </div>
        
        <div class="download-options">
            <!-- <a href="https://testflight.apple.com/join/YOUR_TESTFLIGHT_CODE" class="download-btn">
                📱 Download via TestFlight
            </a> -->

            <a href="TableTennis.ipa" class="download-btn secondary" download>
                📦 Download IPA File
            </a>

            <!-- <p class="download-note">
                <strong>TestFlight:</strong> Recommended for easy installation and automatic updates.<br>
                <strong>IPA File:</strong> For advanced users with developer accounts or jailbroken devices.
            </p> -->
        </div>
        
        <div class="instructions">
            <h3>Installation Instructions</h3>

            <!-- <h4 style="margin-top: 15px; margin-bottom: 8px; font-size: 14px; color: #007AFF;">📱 TestFlight (Recommended)</h4>
            <ol style="margin-bottom: 15px;">
                <li>Tap "Download via TestFlight" above</li>
                <li>Install TestFlight if prompted</li>
                <li>Accept the beta invitation</li>
                <li>Install Table Tennis from TestFlight</li>
                <li>Get automatic updates!</li>
            </ol> -->

            <h4 style="margin-bottom: 8px; font-size: 14px; color: #34C759;">📦 IPA File</h4>
            <ol>
                <li>Tap "Download IPA File" above</li>
                <li>Use AltStore, Sideloadly, or Xcode to install</li>
                <li>Trust the developer certificate in Settings</li>
                <li>Launch the app and enjoy!</li>
            </ol>
        </div>
    </div>

    <script>
        // Add interactive feedback for download buttons
        document.addEventListener('DOMContentLoaded', function() {
            const testflightBtn = document.querySelector('.download-btn:not(.secondary)');
            const ipaBtn = document.querySelector('.download-btn.secondary');

            // TestFlight button
            testflightBtn.addEventListener('click', function(e) {
                const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

                if (!isIOS) {
                    e.preventDefault();
                    alert('TestFlight is only available for iOS devices. Please open this page on your iPhone or iPad, or use the IPA download option.');
                }
            });

            // IPA button
            ipaBtn.addEventListener('click', function(e) {
                // Check if IPA file exists (basic check)
                fetch('TableTennis.ipa', { method: 'HEAD' })
                    .then(response => {
                        if (!response.ok) {
                            e.preventDefault();
                            alert('IPA file not found. Please contact the developer or use TestFlight instead.');
                        }
                    })
                    .catch(() => {
                        e.preventDefault();
                        alert('IPA file not available. Please use TestFlight or contact the developer.');
                    });
            });
        });

        // Add subtle animation on load
        window.addEventListener('load', function() {
            document.querySelector('.container').style.opacity = '0';
            document.querySelector('.container').style.transform = 'translateY(20px)';

            setTimeout(() => {
                document.querySelector('.container').style.transition = 'all 0.6s ease';
                document.querySelector('.container').style.opacity = '1';
                document.querySelector('.container').style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
